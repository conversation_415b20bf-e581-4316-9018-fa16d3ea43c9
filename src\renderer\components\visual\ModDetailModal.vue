<template>
  <!-- Proper modal dialog with backdrop -->
  <div class="modal-backdrop" @click="handleOverlayClick">
    <div class="modal-dialog" @click.stop>
        <!-- Modal Header -->
        <div class="modal-header">
          <div class="header-content">
            <h2 class="modal-title">{{ displayName }}</h2>
            <p class="modal-subtitle">{{ mod.category || 'Unknown Category' }}</p>
          </div>
          <button @click="$emit('close')" class="close-button">
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>

        <!-- Modal Body -->
        <div class="modal-body">
          <!-- Enhanced Thumbnail Section with Variations -->
          <div class="info-section">
            <div class="thumbnail-section">
              <!-- Primary Thumbnail Display -->
              <div v-if="primaryThumbnail" class="large-thumbnail">
                <img
                  :src="primaryThumbnail.imageData"
                  :alt="`${mod.fileName} thumbnail`"
                  class="thumbnail-image"
                />
              </div>
              <div v-else class="thumbnail-placeholder">
                <component :is="getPlaceholderIcon()" class="placeholder-icon" />
                <span class="placeholder-text">{{ getPlaceholderText() }}</span>
              </div>

              <!-- Enhanced Thumbnail Variations Section -->
              <div v-if="hasMultipleVariations" class="variations-section">
                <div class="variations-header">
                  <h3 class="variations-title">
                    <SparklesIcon class="variations-icon" />
                    Color Variations
                    <span class="variations-count">({{ thumbnailVariations.length }})</span>
                  </h3>
                  <p class="variations-subtitle">Multiple styles available for this mod</p>
                </div>
                <ThumbnailVariationDisplay
                  :mod-name="displayName"
                  :primary-thumbnail="primaryThumbnail"
                  :variations="thumbnailVariations"
                  :display-mode="'grid'"
                  :max-visible-variations="8"
                  @thumbnail-selected="handleThumbnailSelected"
                />
              </div>

              <!-- Fallback for No Variations -->
              <div v-else-if="primaryThumbnail" class="no-variations-section">
                <p class="no-variations-text">
                  <PhotoIcon class="no-variations-icon" />
                  Single style available
                </p>
              </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
              <button @click="openInExplorer" class="action-btn primary">
                <FolderOpenIcon class="w-4 h-4" />
                Open in Explorer
              </button>
              <button @click="copyPath" class="action-btn secondary">
                <ClipboardIcon class="w-4 h-4" />
                Copy Path
              </button>
              <button v-if="mod.isCorrupted" @click="deleteMod" class="action-btn danger">
                <TrashIcon class="w-4 h-4" />
                Delete
              </button>
            </div>
          </div>

          <div class="basic-info">
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">File Name:</span>
                <span class="info-value">{{ mod.fileName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">File Size:</span>
                <span class="info-value">{{ formatFileSize(mod.fileSize) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Resources:</span>
                <span class="info-value">{{ mod.resourceCount || 0 }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Last Modified:</span>
                <span class="info-value">{{ formatDate(mod.lastModified) }}</span>
              </div>
              <div v-if="mod.author" class="info-item">
                <span class="info-label">Author:</span>
                <span class="info-value">{{ mod.author }}</span>
              </div>
              <div v-if="mod.version" class="info-item">
                <span class="info-label">Version:</span>
                <span class="info-value">{{ mod.version }}</span>
              </div>
            </div>

            <!-- Status Badges -->
            <div class="status-badges">
              <span v-if="mod.hasConflicts" class="badge badge-warning">
                <ExclamationTriangleIcon class="w-4 h-4" />
                Has Conflicts
              </span>
              <span v-if="mod.isCorrupted" class="badge badge-error">
                <XCircleIcon class="w-4 h-4" />
                Corrupted
              </span>
              <span v-if="isHighQuality" class="badge badge-success">
                <SparklesIcon class="w-4 h-4" />
                High Quality
              </span>
            </div>
          </div>
        </div>

        <!-- Enhanced Content Analysis -->
        <div v-if="hasEnhancedContent" class="enhanced-section">
          <h3 class="section-title">Content Analysis</h3>
          
          <!-- Object Classification -->
          <div v-if="mod.objectClassification" class="classification-card">
            <h4 class="classification-title">Object Classification</h4>
            <div class="classification-content">
              <div class="classification-item">
                <span class="classification-label">Category:</span>
                <span class="classification-value">{{ mod.objectClassification.category }}</span>
              </div>
              <div class="classification-item">
                <span class="classification-label">Specific Type:</span>
                <span class="classification-value">{{ mod.objectClassification.specificType }}</span>
              </div>
              <div v-if="mod.objectClassification.roomAssignment" class="classification-item">
                <span class="classification-label">Room Assignment:</span>
                <span class="classification-value">{{ mod.objectClassification.roomAssignment }}</span>
              </div>
              <div class="classification-item">
                <span class="classification-label">Confidence:</span>
                <div class="confidence-bar">
                  <div 
                    class="confidence-fill" 
                    :style="{ width: `${(mod.objectClassification.confidence * 100)}%` }"
                  ></div>
                  <span class="confidence-text">{{ Math.round(mod.objectClassification.confidence * 100) }}%</span>
                </div>
              </div>
              <div v-if="mod.objectClassification.functionality?.length" class="classification-item">
                <span class="classification-label">Functionality:</span>
                <div class="functionality-tags">
                  <span 
                    v-for="func in mod.objectClassification.functionality" 
                    :key="func" 
                    class="functionality-tag"
                  >
                    {{ func }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- CAS Classification -->
          <div v-if="mod.universalClassification" class="classification-card">
            <h4 class="classification-title">CAS Classification</h4>
            <div class="classification-content">
              <div class="classification-item">
                <span class="classification-label">Category:</span>
                <span class="classification-value">{{ mod.universalClassification.category }}</span>
              </div>
              <div v-if="mod.universalClassification.subcategory" class="classification-item">
                <span class="classification-label">Subcategory:</span>
                <span class="classification-value">{{ mod.universalClassification.subcategory }}</span>
              </div>
              <div class="classification-item">
                <span class="classification-label">Confidence:</span>
                <div class="confidence-bar">
                  <div 
                    class="confidence-fill" 
                    :style="{ width: `${(mod.universalClassification.confidence * 100)}%` }"
                  ></div>
                  <span class="confidence-text">{{ Math.round(mod.universalClassification.confidence * 100) }}%</span>
                </div>
              </div>
              <div v-if="mod.universalClassification.ageGroups?.length" class="classification-item">
                <span class="classification-label">Age Groups:</span>
                <div class="age-group-tags">
                  <span 
                    v-for="age in mod.universalClassification.ageGroups" 
                    :key="age" 
                    class="age-group-tag"
                  >
                    {{ age }}
                  </span>
                </div>
              </div>
              <div v-if="mod.universalClassification.genders?.length" class="classification-item">
                <span class="classification-label">Genders:</span>
                <div class="gender-tags">
                  <span 
                    v-for="gender in mod.universalClassification.genders" 
                    :key="gender" 
                    class="gender-tag"
                  >
                    {{ gender }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div v-if="mod.description" class="description-section">
          <h3 class="section-title">Description</h3>
          <p class="description-text">{{ mod.description }}</p>
        </div>

        <!-- Resource Breakdown -->
        <div v-if="mod.resourceBreakdown" class="resources-section">
          <h3 class="section-title">Resource Breakdown</h3>
          <div class="resource-grid">
            <div 
              v-for="(count, type) in mod.resourceBreakdown" 
              :key="type" 
              class="resource-item"
            >
              <span class="resource-type">{{ formatResourceType(type) }}:</span>
              <span class="resource-count">{{ count }}</span>
            </div>
          </div>
        </div>

        <!-- Conflicts and Issues -->
        <div v-if="mod.conflicts?.length || mod.issues?.length" class="issues-section">
          <h3 class="section-title">Issues & Conflicts</h3>
          
          <div v-if="mod.conflicts?.length" class="conflicts-list">
            <h4 class="subsection-title">Conflicts</h4>
            <div 
              v-for="conflict in mod.conflicts" 
              :key="conflict.id" 
              class="conflict-item"
            >
              <ExclamationTriangleIcon class="w-4 h-4 text-yellow-500" />
              <span>{{ conflict.description }}</span>
            </div>
          </div>

          <div v-if="mod.issues?.length" class="issues-list">
            <h4 class="subsection-title">Issues</h4>
            <div 
              v-for="issue in mod.issues" 
              :key="issue" 
              class="issue-item"
            >
              <XCircleIcon class="w-4 h-4 text-red-500" />
              <span>{{ issue }}</span>
            </div>
          </div>
        </div>

      <!-- Modal Footer -->
      <div class="modal-footer">
        <button @click="$emit('close')" class="footer-btn secondary">
          Close
        </button>
        <button @click="analyzeAgain" class="footer-btn primary">
          <ArrowPathIcon class="w-4 h-4" />
          Re-analyze
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  XMarkIcon,
  FolderOpenIcon,
  ClipboardIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  SparklesIcon,
  ArrowPathIcon,
  DocumentIcon,
  UserIcon,
  HomeIcon,
  CogIcon,
  PuzzlePieceIcon,
  PhotoIcon
} from '@heroicons/vue/24/outline';

import type { ModData } from '../../../types/ModData';
import type { ThumbnailData } from '../../../services/visual/ThumbnailExtractionService';
import ThumbnailVariationDisplay from './ThumbnailVariationDisplay.vue';

// Props
interface Props {
  mod: ModData;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
  delete: [modId: string];
  reanalyze: [modId: string];
}>();

// Enhanced thumbnail data from props
const primaryThumbnail = computed(() => {
  return props.mod.primaryThumbnail ||
         (props.mod.thumbnails && props.mod.thumbnails[0]) ||
         null;
});

const thumbnailVariations = computed(() => {
  return props.mod.thumbnailVariations || [];
});

const hasMultipleVariations = computed(() => {
  return props.mod.hasMultipleVariations || thumbnailVariations.value.length > 0;
});

// Computed properties
const displayName = computed(() => {
  return props.mod.fileName.replace(/\.(package|ts4script)$/i, '').replace(/_/g, ' ');
});

const isHighQuality = computed(() => {
  const hasHighResources = (props.mod.resourceCount || 0) > 50;
  const hasLargeSize = (props.mod.fileSize || 0) > 1024 * 1024;
  const hasEnhancedAnalysis = props.mod.objectClassification?.confidence > 0.8;
  
  return hasHighResources || hasLargeSize || hasEnhancedAnalysis;
});

const hasEnhancedContent = computed(() => {
  return props.mod.objectClassification || props.mod.universalClassification;
});

// Methods
const handleOverlayClick = () => {
  emit('close');
};

const openInExplorer = () => {
  window.electronAPI?.openInExplorer?.(props.mod.filePath);
};

const copyPath = async () => {
  try {
    await navigator.clipboard.writeText(props.mod.filePath);
    // Could show a toast notification here
  } catch (error) {
    console.error('Failed to copy path:', error);
  }
};

const deleteMod = () => {
  if (confirm(`Are you sure you want to delete "${props.mod.fileName}"?`)) {
    emit('delete', props.mod.id);
    emit('close');
  }
};

const analyzeAgain = () => {
  emit('reanalyze', props.mod.id);
};

const getPlaceholderIcon = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return UserIcon;
  if (category.includes('build') || category.includes('buy')) return HomeIcon;
  if (category.includes('script')) return CogIcon;
  if (category.includes('gameplay')) return PuzzlePieceIcon;
  
  return DocumentIcon;
};

const getPlaceholderText = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas')) return 'CAS Content';
  if (category.includes('build') || category.includes('buy')) return 'Build/Buy Content';
  if (category.includes('script')) return 'Script Mod';
  if (category.includes('gameplay')) return 'Gameplay Mod';
  
  return 'Mod Content';
};

const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

const formatDate = (timestamp: number | undefined): string => {
  if (!timestamp) return 'Unknown';
  
  const date = new Date(timestamp);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

const formatResourceType = (type: string): string => {
  return type.charAt(0).toUpperCase() + type.slice(1).replace(/([A-Z])/g, ' $1');
};

// Enhanced thumbnail event handlers
const handleThumbnailSelected = (thumbnail: ThumbnailData) => {
  console.log(`🎨 [ModDetailModal] Selected thumbnail variation: ${thumbnail.resourceType}`);
  // The ThumbnailVariationDisplay component handles the visual feedback
};

// Lifecycle - Modern Vue 3 approach
onMounted(() => {
  console.log(`🚀 [ModDetailModal] MODAL MOUNTED for ${props.mod.fileName}:`, {
    hasPrimaryThumbnail: !!primaryThumbnail.value,
    variationsCount: thumbnailVariations.value.length,
    hasMultipleVariations: hasMultipleVariations.value
  });

  // Add visual debugging
  const modalElement = document.querySelector('.modal-overlay');
  if (modalElement) {
    console.log('✅ [ModDetailModal] Modal element found in DOM');
    console.log('📏 [ModDetailModal] Modal element styles:', {
      display: getComputedStyle(modalElement).display,
      visibility: getComputedStyle(modalElement).visibility,
      zIndex: getComputedStyle(modalElement).zIndex,
      position: getComputedStyle(modalElement).position
    });
  } else {
    console.error('❌ [ModDetailModal] Modal element NOT found in DOM');
  }
});
</script>

<style scoped>
/* ===== MODAL BACKDROP & DIALOG ===== */
/* Modern modal implementation following Simonitor design system */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(51, 49, 46, 0.85) 0%,
    rgba(51, 49, 46, 0.75) 50%,
    rgba(51, 49, 46, 0.85) 100%
  );
  backdrop-filter: blur(16px) saturate(1.3) brightness(0.9);
  -webkit-backdrop-filter: blur(16px) saturate(1.3) brightness(0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-6);
  z-index: 9999;
  animation: modalBackdropFadeIn var(--duration-400) var(--ease-out);
  cursor: pointer;
}

@keyframes modalBackdropFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px) saturate(1) brightness(1);
    -webkit-backdrop-filter: blur(0px) saturate(1) brightness(1);
    transform: scale(1.02);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(16px) saturate(1.3) brightness(0.9);
    -webkit-backdrop-filter: blur(16px) saturate(1.3) brightness(0.9);
    transform: scale(1);
  }
}

/* ===== PROFESSIONAL MODAL DIALOG ===== */
.modal-dialog {
  background: var(--card-bg);
  border: 2px solid var(--card-border);
  border-radius: var(--radius-2xl);
  box-shadow:
    var(--shadow-2xl),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 25px 50px -12px rgba(0, 0, 0, 0.25);
  max-width: 960px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: translateY(0);
  animation: modalDialogSlideIn var(--duration-400) var(--ease-out);
  cursor: default;
  position: relative;
}

.modal-dialog::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  z-index: 1;
}

@keyframes modalDialogSlideIn {
  from {
    opacity: 0;
    transform: translateY(var(--space-12)) scale(0.92);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

/* ===== ENHANCED MODAL HEADER ===== */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-8) var(--space-6) var(--space-6) var(--space-6);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(
    135deg,
    var(--bg-elevated) 0%,
    var(--bg-primary) 100%
  );
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
  position: relative;
  z-index: 2;
}

.modal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: var(--space-6);
  right: var(--space-6);
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--border-light) 20%,
    var(--border-light) 80%,
    transparent 100%
  );
}

.header-content {
  flex: 1;
  min-width: 0; /* Prevent text overflow */
}

.modal-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--leading-tight);
  /* Ensure text doesn't overflow */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: linear-gradient(
    135deg,
    var(--text-primary) 0%,
    var(--primary) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-subtitle {
  color: var(--text-secondary);
  margin-top: var(--space-2);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.8;
}

/* ===== ENHANCED CLOSE BUTTON ===== */
.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--space-12);
  height: var(--space-12);
  padding: 0;
  color: var(--text-muted);
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--duration-300) var(--ease-out);
  flex-shrink: 0;
  margin-left: var(--space-4);
  position: relative;
  overflow: hidden;
}

.close-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(0, 0, 0, 0.05) 100%
  );
  opacity: 0;
  transition: opacity var(--duration-200) var(--ease-out);
}

.close-button:hover {
  color: var(--error);
  background: var(--error-bg);
  border-color: var(--error-border);
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    var(--shadow-md),
    0 0 0 4px rgba(var(--error-rgb), 0.1);
}

.close-button:hover::before {
  opacity: 1;
}

.close-button:active {
  transform: translateY(-1px) scale(1.02);
  box-shadow: var(--shadow-sm);
}

.close-button:focus {
  outline: 2px solid var(--focus-ring);
  outline-offset: 3px;
}

/* ===== MODAL BODY ===== */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-6);
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--border-medium) var(--bg-secondary);
}

.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

.modal-body::-webkit-scrollbar-thumb {
  background: var(--border-medium);
  border-radius: var(--radius-full);
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--border-strong);
}

/* ===== INFO SECTION LAYOUT ===== */
.info-section {
  display: flex;
  gap: var(--space-6);
  align-items: flex-start;
}

@media (max-width: 768px) {
  .info-section {
    flex-direction: column;
    gap: var(--space-4);
  }
}

/* ===== ENHANCED THUMBNAIL SECTION ===== */
.thumbnail-section {
  flex-shrink: 0;
  position: relative;
}

/* ===== LARGE THUMBNAIL WITH PROFESSIONAL STYLING ===== */
.large-thumbnail {
  width: 320px;
  height: 320px;
  border-radius: var(--radius-xl);
  overflow: hidden;
  margin-bottom: var(--space-6);
  border: 2px solid var(--border-light);
  box-shadow:
    var(--shadow-lg),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  background: var(--bg-elevated);
  position: relative;
  transition: all var(--duration-300) var(--ease-out);
  transform: translateZ(0); /* GPU acceleration */
}

.large-thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(0, 0, 0, 0.05) 100%
  );
  pointer-events: none;
  z-index: 1;
}

.large-thumbnail:hover {
  border-color: var(--primary-border);
  box-shadow:
    var(--shadow-xl),
    0 0 0 1px var(--primary),
    0 0 20px rgba(var(--primary-rgb), 0.1);
  transform: translateY(-2px) scale(1.01);
}

@media (max-width: 768px) {
  .large-thumbnail {
    width: 100%;
    max-width: 360px;
    height: 360px;
    margin: 0 auto var(--space-6) auto;
  }
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform var(--duration-300) var(--ease-out);
  position: relative;
  z-index: 0;
}

.large-thumbnail:hover .thumbnail-image {
  transform: scale(1.03);
}

/* ===== ENHANCED PLACEHOLDER WITH CATEGORY ICONS ===== */
.thumbnail-placeholder {
  width: 320px;
  height: 320px;
  border-radius: var(--radius-xl);
  background: linear-gradient(
    135deg,
    var(--bg-secondary) 0%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 100%
  );
  border: 2px dashed var(--border-medium);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-sm);
  position: relative;
  transition: all var(--duration-300) var(--ease-out);
}

.thumbnail-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

.thumbnail-placeholder:hover {
  border-color: var(--border-strong);
  background: linear-gradient(
    135deg,
    var(--bg-tertiary) 0%,
    var(--bg-secondary) 50%,
    var(--bg-tertiary) 100%
  );
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .thumbnail-placeholder {
    width: 100%;
    max-width: 360px;
    height: 360px;
    margin: 0 auto var(--space-6) auto;
  }
}

.placeholder-icon {
  width: var(--space-20);
  height: var(--space-20);
  margin-bottom: var(--space-4);
  opacity: 0.7;
  color: var(--text-secondary);
  transition: all var(--duration-200) var(--ease-out);
}

.thumbnail-placeholder:hover .placeholder-icon {
  opacity: 1;
  transform: scale(1.1);
  color: var(--text-primary);
}

.placeholder-text {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-secondary);
  text-align: center;
  line-height: 1.4;
  transition: color var(--duration-200) var(--ease-out);
}

.thumbnail-placeholder:hover .placeholder-text {
  color: var(--text-primary);
}

/* ===== ENHANCED VARIATIONS SECTION ===== */
.variations-section {
  margin-top: var(--space-6);
  padding: var(--space-5);
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
}

.variations-header {
  margin-bottom: var(--space-4);
  text-align: center;
}

.variations-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.variations-icon {
  width: var(--space-5);
  height: var(--space-5);
  color: var(--primary);
}

.variations-count {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  background: var(--primary-bg);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  border: 1px solid var(--primary-border);
}

.variations-subtitle {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin: 0;
  font-style: italic;
}

/* ===== NO VARIATIONS FALLBACK ===== */
.no-variations-section {
  margin-top: var(--space-4);
  padding: var(--space-4);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  text-align: center;
}

.no-variations-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin: 0;
  font-style: italic;
}

.no-variations-icon {
  width: var(--space-4);
  height: var(--space-4);
  opacity: 0.7;
}

/* ===== ACTION BUTTONS ===== */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  width: 100%;
}

.action-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  font-family: var(--font-family-sans);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all var(--duration-200) var(--ease-out);
  transform: translateY(0);
  box-shadow: var(--button-shadow);
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--button-shadow-hover);
}

.action-btn:focus {
  outline: 2px solid var(--focus-ring);
  outline-offset: 2px;
}

.action-btn.primary {
  background: var(--success);
  color: var(--success-foreground);
  border-color: var(--success);
}

.action-btn.primary:hover {
  background: var(--success-hover);
  border-color: var(--success-hover);
}

.action-btn.secondary {
  background: var(--secondary-bg);
  color: var(--secondary);
  border-color: var(--secondary-border);
}

.action-btn.secondary:hover {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border-color: var(--secondary);
}

.action-btn.danger {
  background: var(--error);
  color: var(--error-foreground);
  border-color: var(--error);
}

.action-btn.danger:hover {
  background: var(--error-hover);
  border-color: var(--error-hover);
}

/* ===== BASIC INFO SECTION ===== */
.basic-info {
  flex: 1;
  min-width: 0; /* Prevent overflow */
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

@media (max-width: 640px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
}

.info-item {
  display: flex;
  flex-direction: column;
  padding: var(--space-3);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  transition: all var(--duration-200) var(--ease-out);
}

.info-item:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.info-label {
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  color: var(--text-muted);
  margin-bottom: var(--space-1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.info-value {
  color: var(--text-primary);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
  word-break: break-word;
}

/* ===== STATUS BADGES ===== */
.status-badges {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.badge {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  border: 1px solid transparent;
  transition: all var(--duration-200) var(--ease-out);
}

.badge-warning {
  background: var(--warning-bg);
  color: var(--warning);
  border-color: var(--warning-border);
}

.badge-error {
  background: var(--error-bg);
  color: var(--error);
  border-color: var(--error-border);
}

.badge-success {
  background: var(--success-bg);
  color: var(--success);
  border-color: var(--success-border);
}

/* ===== ENHANCED CONTENT SECTIONS ===== */
.enhanced-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  padding: var(--space-6);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  margin-top: var(--space-2);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.section-title::before {
  content: '';
  width: 4px;
  height: var(--space-6);
  background: linear-gradient(135deg, var(--coral-500), var(--teal-500));
  border-radius: var(--radius-full);
}

.classification-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  transition: all var(--duration-200) var(--ease-out);
}

.classification-card:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.classification-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.classification-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.classification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-light);
}

.classification-item:last-child {
  border-bottom: none;
}

.classification-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  min-width: 120px;
}

.classification-value {
  color: var(--text-primary);
  font-weight: var(--font-medium);
  font-size: var(--text-sm);
}

/* ===== CONFIDENCE BAR ===== */
.confidence-bar {
  position: relative;
  width: 140px;
  height: 20px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success) 0%, var(--success-hover) 100%);
  transition: all var(--duration-300) var(--ease-out);
  border-radius: var(--radius-full);
}

.confidence-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===== TAG GROUPS ===== */
.functionality-tags, .age-group-tags, .gender-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-top: var(--space-1);
}

.functionality-tag, .age-group-tag, .gender-tag {
  padding: var(--space-1) var(--space-2);
  background: var(--primary-bg);
  color: var(--primary);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border: 1px solid var(--primary-border);
  border-radius: var(--radius-md);
  transition: all var(--duration-200) var(--ease-out);
}

.functionality-tag:hover, .age-group-tag:hover, .gender-tag:hover {
  background: var(--primary);
  color: var(--primary-foreground);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* ===== OTHER CONTENT SECTIONS ===== */
.description-section, .resources-section, .issues-section {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  padding: var(--space-6);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  margin-top: var(--space-2);
}

.description-text {
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  font-size: var(--text-base);
  padding: var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary);
}

/* ===== RESOURCE GRID ===== */
.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

@media (max-width: 640px) {
  .resource-grid {
    grid-template-columns: 1fr;
  }
}

.resource-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  transition: all var(--duration-200) var(--ease-out);
}

.resource-item:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.resource-type {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.resource-count {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--coral-500), var(--teal-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== SUBSECTIONS ===== */
.subsection-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: var(--space-4) 0 var(--space-3) 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.conflicts-list, .issues-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.conflict-item, .issue-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  transition: all var(--duration-200) var(--ease-out);
}

.conflict-item:hover, .issue-item:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.conflict-item {
  border-left: 4px solid var(--warning);
}

.issue-item {
  border-left: 4px solid var(--error);
}

/* ===== MODAL FOOTER ===== */
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-3);
  padding: var(--space-6);
  border-top: 1px solid var(--border-light);
  background: var(--bg-elevated);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

.footer-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  font-family: var(--font-family-sans);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  border: 1px solid transparent;
  transition: all var(--duration-200) var(--ease-out);
  transform: translateY(0);
  box-shadow: var(--button-shadow);
}

.footer-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--button-shadow-hover);
}

.footer-btn:focus {
  outline: 2px solid var(--focus-ring);
  outline-offset: 2px;
}

.footer-btn.primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border-color: var(--primary);
}

.footer-btn.primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.footer-btn.secondary {
  background: var(--secondary-bg);
  color: var(--secondary);
  border-color: var(--secondary-border);
}

.footer-btn.secondary:hover {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border-color: var(--secondary);
}

/* ===== THUMBNAIL VARIATIONS SECTION ===== */
.variations-section {
  margin-top: var(--space-6);
  padding: var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  transition: all var(--duration-200) var(--ease-out);
}

.variations-section:hover {
  border-color: var(--border-medium);
  box-shadow: var(--shadow-sm);
}

.variations-title {
  font-size: var(--text-lg);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.variations-title::before {
  content: '🎨';
  font-size: var(--text-base);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .modal-dialog {
    max-width: 95vw;
    margin: var(--space-4);
  }
}

@media (max-width: 768px) {
  .modal-backdrop {
    padding: var(--space-4);
  }

  .modal-dialog {
    max-height: 95vh;
  }

  .modal-header {
    padding: var(--space-4);
  }

  .modal-body {
    padding: var(--space-4);
    gap: var(--space-4);
  }

  .modal-footer {
    padding: var(--space-4);
    flex-direction: column-reverse;
    gap: var(--space-2);
  }

  .footer-btn {
    width: 100%;
    justify-content: center;
  }

  .enhanced-section,
  .description-section,
  .resources-section,
  .issues-section {
    padding: var(--space-4);
  }
}

@media (max-width: 480px) {
  .modal-title {
    font-size: var(--text-xl);
  }

  .section-title {
    font-size: var(--text-lg);
  }

  .classification-title {
    font-size: var(--text-base);
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  .modal-backdrop,
  .modal-dialog,
  .thumbnail-image,
  .action-btn,
  .footer-btn,
  .resource-item,
  .classification-card,
  .info-item,
  .conflict-item,
  .issue-item,
  .variations-section {
    animation: none;
    transition: none;
  }

  .action-btn:hover,
  .footer-btn:hover,
  .resource-item:hover {
    transform: none;
  }
}

@media (prefers-contrast: high) {
  .modal-dialog {
    border-width: 2px;
    border-color: var(--text-primary);
  }

  .modal-header,
  .modal-footer {
    border-width: 2px;
  }

  .action-btn,
  .footer-btn {
    border-width: 2px;
    font-weight: var(--font-bold);
  }
}
</style>
