<template>
  <!-- Proper modal dialog with backdrop -->
  <div class="modal-backdrop" @click="handleOverlayClick">
    <div class="modal-dialog" @click.stop>
        <!-- Modal Header -->
        <div class="modal-header">
          <div class="header-content">
            <h2 class="modal-title">{{ displayName }}</h2>
            <p class="modal-subtitle">{{ mod.category || 'Unknown Category' }}</p>
          </div>
          <button @click="$emit('close')" class="close-button">
            <XMarkIcon class="w-6 h-6" />
          </button>
        </div>

        <!-- Modal Body -->
        <div class="modal-body">
          <!-- Enhanced Thumbnail Section with Variations -->
          <div class="info-section">
            <div class="thumbnail-section">
              <!-- Primary Thumbnail Display -->
              <div v-if="primaryThumbnail" class="large-thumbnail">
                <img
                  :src="primaryThumbnail.imageData"
                  :alt="`${mod.fileName} thumbnail`"
                  class="thumbnail-image"
                />
              </div>
              <div v-else class="thumbnail-placeholder">
                <component :is="getPlaceholderIcon()" class="placeholder-icon" />
                <span class="placeholder-text">{{ getPlaceholderText() }}</span>
              </div>

              <!-- Thumbnail Variations Carousel -->
              <div v-if="hasMultipleVariations" class="variations-section">
                <h3 class="variations-title">Color Variations ({{ thumbnailVariations.length }})</h3>
                <ThumbnailVariationDisplay
                  :mod-name="displayName"
                  :primary-thumbnail="primaryThumbnail"
                  :variations="thumbnailVariations"
                  :display-mode="'carousel'"
                  :max-visible-variations="8"
                  @thumbnail-selected="handleThumbnailSelected"
                />
              </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="action-buttons">
              <button @click="openInExplorer" class="action-btn primary">
                <FolderOpenIcon class="w-4 h-4" />
                Open in Explorer
              </button>
              <button @click="copyPath" class="action-btn secondary">
                <ClipboardIcon class="w-4 h-4" />
                Copy Path
              </button>
              <button v-if="mod.isCorrupted" @click="deleteMod" class="action-btn danger">
                <TrashIcon class="w-4 h-4" />
                Delete
              </button>
            </div>
          </div>

          <div class="basic-info">
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">File Name:</span>
                <span class="info-value">{{ mod.fileName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">File Size:</span>
                <span class="info-value">{{ formatFileSize(mod.fileSize) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Resources:</span>
                <span class="info-value">{{ mod.resourceCount || 0 }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Last Modified:</span>
                <span class="info-value">{{ formatDate(mod.lastModified) }}</span>
              </div>
              <div v-if="mod.author" class="info-item">
                <span class="info-label">Author:</span>
                <span class="info-value">{{ mod.author }}</span>
              </div>
              <div v-if="mod.version" class="info-item">
                <span class="info-label">Version:</span>
                <span class="info-value">{{ mod.version }}</span>
              </div>
            </div>

            <!-- Status Badges -->
            <div class="status-badges">
              <span v-if="mod.hasConflicts" class="badge badge-warning">
                <ExclamationTriangleIcon class="w-4 h-4" />
                Has Conflicts
              </span>
              <span v-if="mod.isCorrupted" class="badge badge-error">
                <XCircleIcon class="w-4 h-4" />
                Corrupted
              </span>
              <span v-if="isHighQuality" class="badge badge-success">
                <SparklesIcon class="w-4 h-4" />
                High Quality
              </span>
            </div>
          </div>
        </div>

        <!-- Enhanced Content Analysis -->
        <div v-if="hasEnhancedContent" class="enhanced-section">
          <h3 class="section-title">Content Analysis</h3>
          
          <!-- Object Classification -->
          <div v-if="mod.objectClassification" class="classification-card">
            <h4 class="classification-title">Object Classification</h4>
            <div class="classification-content">
              <div class="classification-item">
                <span class="classification-label">Category:</span>
                <span class="classification-value">{{ mod.objectClassification.category }}</span>
              </div>
              <div class="classification-item">
                <span class="classification-label">Specific Type:</span>
                <span class="classification-value">{{ mod.objectClassification.specificType }}</span>
              </div>
              <div v-if="mod.objectClassification.roomAssignment" class="classification-item">
                <span class="classification-label">Room Assignment:</span>
                <span class="classification-value">{{ mod.objectClassification.roomAssignment }}</span>
              </div>
              <div class="classification-item">
                <span class="classification-label">Confidence:</span>
                <div class="confidence-bar">
                  <div 
                    class="confidence-fill" 
                    :style="{ width: `${(mod.objectClassification.confidence * 100)}%` }"
                  ></div>
                  <span class="confidence-text">{{ Math.round(mod.objectClassification.confidence * 100) }}%</span>
                </div>
              </div>
              <div v-if="mod.objectClassification.functionality?.length" class="classification-item">
                <span class="classification-label">Functionality:</span>
                <div class="functionality-tags">
                  <span 
                    v-for="func in mod.objectClassification.functionality" 
                    :key="func" 
                    class="functionality-tag"
                  >
                    {{ func }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- CAS Classification -->
          <div v-if="mod.universalClassification" class="classification-card">
            <h4 class="classification-title">CAS Classification</h4>
            <div class="classification-content">
              <div class="classification-item">
                <span class="classification-label">Category:</span>
                <span class="classification-value">{{ mod.universalClassification.category }}</span>
              </div>
              <div v-if="mod.universalClassification.subcategory" class="classification-item">
                <span class="classification-label">Subcategory:</span>
                <span class="classification-value">{{ mod.universalClassification.subcategory }}</span>
              </div>
              <div class="classification-item">
                <span class="classification-label">Confidence:</span>
                <div class="confidence-bar">
                  <div 
                    class="confidence-fill" 
                    :style="{ width: `${(mod.universalClassification.confidence * 100)}%` }"
                  ></div>
                  <span class="confidence-text">{{ Math.round(mod.universalClassification.confidence * 100) }}%</span>
                </div>
              </div>
              <div v-if="mod.universalClassification.ageGroups?.length" class="classification-item">
                <span class="classification-label">Age Groups:</span>
                <div class="age-group-tags">
                  <span 
                    v-for="age in mod.universalClassification.ageGroups" 
                    :key="age" 
                    class="age-group-tag"
                  >
                    {{ age }}
                  </span>
                </div>
              </div>
              <div v-if="mod.universalClassification.genders?.length" class="classification-item">
                <span class="classification-label">Genders:</span>
                <div class="gender-tags">
                  <span 
                    v-for="gender in mod.universalClassification.genders" 
                    :key="gender" 
                    class="gender-tag"
                  >
                    {{ gender }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div v-if="mod.description" class="description-section">
          <h3 class="section-title">Description</h3>
          <p class="description-text">{{ mod.description }}</p>
        </div>

        <!-- Resource Breakdown -->
        <div v-if="mod.resourceBreakdown" class="resources-section">
          <h3 class="section-title">Resource Breakdown</h3>
          <div class="resource-grid">
            <div 
              v-for="(count, type) in mod.resourceBreakdown" 
              :key="type" 
              class="resource-item"
            >
              <span class="resource-type">{{ formatResourceType(type) }}:</span>
              <span class="resource-count">{{ count }}</span>
            </div>
          </div>
        </div>

        <!-- Conflicts and Issues -->
        <div v-if="mod.conflicts?.length || mod.issues?.length" class="issues-section">
          <h3 class="section-title">Issues & Conflicts</h3>
          
          <div v-if="mod.conflicts?.length" class="conflicts-list">
            <h4 class="subsection-title">Conflicts</h4>
            <div 
              v-for="conflict in mod.conflicts" 
              :key="conflict.id" 
              class="conflict-item"
            >
              <ExclamationTriangleIcon class="w-4 h-4 text-yellow-500" />
              <span>{{ conflict.description }}</span>
            </div>
          </div>

          <div v-if="mod.issues?.length" class="issues-list">
            <h4 class="subsection-title">Issues</h4>
            <div 
              v-for="issue in mod.issues" 
              :key="issue" 
              class="issue-item"
            >
              <XCircleIcon class="w-4 h-4 text-red-500" />
              <span>{{ issue }}</span>
            </div>
          </div>
        </div>

      <!-- Modal Footer -->
      <div class="modal-footer">
        <button @click="$emit('close')" class="footer-btn secondary">
          Close
        </button>
        <button @click="analyzeAgain" class="footer-btn primary">
          <ArrowPathIcon class="w-4 h-4" />
          Re-analyze
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import {
  XMarkIcon,
  FolderOpenIcon,
  ClipboardIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  SparklesIcon,
  ArrowPathIcon,
  DocumentIcon,
  UserIcon,
  HomeIcon,
  CogIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';

import type { ModData } from '../../../types/ModData';
import type { ThumbnailData } from '../../../services/visual/ThumbnailExtractionService';
import ThumbnailVariationDisplay from './ThumbnailVariationDisplay.vue';

// Props
interface Props {
  mod: ModData;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
  delete: [modId: string];
  reanalyze: [modId: string];
}>();

// Enhanced thumbnail data from props
const primaryThumbnail = computed(() => {
  return props.mod.primaryThumbnail ||
         (props.mod.thumbnails && props.mod.thumbnails[0]) ||
         null;
});

const thumbnailVariations = computed(() => {
  return props.mod.thumbnailVariations || [];
});

const hasMultipleVariations = computed(() => {
  return props.mod.hasMultipleVariations || thumbnailVariations.value.length > 0;
});

// Computed properties
const displayName = computed(() => {
  return props.mod.fileName.replace(/\.(package|ts4script)$/i, '').replace(/_/g, ' ');
});

const isHighQuality = computed(() => {
  const hasHighResources = (props.mod.resourceCount || 0) > 50;
  const hasLargeSize = (props.mod.fileSize || 0) > 1024 * 1024;
  const hasEnhancedAnalysis = props.mod.objectClassification?.confidence > 0.8;
  
  return hasHighResources || hasLargeSize || hasEnhancedAnalysis;
});

const hasEnhancedContent = computed(() => {
  return props.mod.objectClassification || props.mod.universalClassification;
});

// Methods
const handleOverlayClick = () => {
  emit('close');
};

const openInExplorer = () => {
  window.electronAPI?.openInExplorer?.(props.mod.filePath);
};

const copyPath = async () => {
  try {
    await navigator.clipboard.writeText(props.mod.filePath);
    // Could show a toast notification here
  } catch (error) {
    console.error('Failed to copy path:', error);
  }
};

const deleteMod = () => {
  if (confirm(`Are you sure you want to delete "${props.mod.fileName}"?`)) {
    emit('delete', props.mod.id);
    emit('close');
  }
};

const analyzeAgain = () => {
  emit('reanalyze', props.mod.id);
};

const getPlaceholderIcon = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return UserIcon;
  if (category.includes('build') || category.includes('buy')) return HomeIcon;
  if (category.includes('script')) return CogIcon;
  if (category.includes('gameplay')) return PuzzlePieceIcon;
  
  return DocumentIcon;
};

const getPlaceholderText = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas')) return 'CAS Content';
  if (category.includes('build') || category.includes('buy')) return 'Build/Buy Content';
  if (category.includes('script')) return 'Script Mod';
  if (category.includes('gameplay')) return 'Gameplay Mod';
  
  return 'Mod Content';
};

const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

const formatDate = (timestamp: number | undefined): string => {
  if (!timestamp) return 'Unknown';
  
  const date = new Date(timestamp);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

const formatResourceType = (type: string): string => {
  return type.charAt(0).toUpperCase() + type.slice(1).replace(/([A-Z])/g, ' $1');
};

// Enhanced thumbnail event handlers
const handleThumbnailSelected = (thumbnail: ThumbnailData) => {
  console.log(`🎨 [ModDetailModal] Selected thumbnail variation: ${thumbnail.resourceType}`);
  // The ThumbnailVariationDisplay component handles the visual feedback
};

// Lifecycle - Modern Vue 3 approach
onMounted(() => {
  console.log(`🚀 [ModDetailModal] MODAL MOUNTED for ${props.mod.fileName}:`, {
    hasPrimaryThumbnail: !!primaryThumbnail.value,
    variationsCount: thumbnailVariations.value.length,
    hasMultipleVariations: hasMultipleVariations.value
  });

  // Add visual debugging
  const modalElement = document.querySelector('.modal-overlay');
  if (modalElement) {
    console.log('✅ [ModDetailModal] Modal element found in DOM');
    console.log('📏 [ModDetailModal] Modal element styles:', {
      display: getComputedStyle(modalElement).display,
      visibility: getComputedStyle(modalElement).visibility,
      zIndex: getComputedStyle(modalElement).zIndex,
      position: getComputedStyle(modalElement).position
    });
  } else {
    console.error('❌ [ModDetailModal] Modal element NOT found in DOM');
  }
});
</script>

<style scoped>
/* Modal backdrop - covers entire screen */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  z-index: 9999;
}

/* Modal dialog - the actual modal content */
.modal-dialog {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Remove old modal-container styles - now using modal-dialog */

/* Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: white;
}

.header-content {
  flex: 1;
}

.modal-title {
  font-size: 24px;
  font-weight: bold;
  color: #111827;
  margin: 0;
}

.modal-subtitle {
  color: #6b7280;
  margin-top: 4px;
  font-size: 14px;
}

.close-button {
  padding: 8px;
  color: #6b7280;
  background: none;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.close-button:hover {
  color: #374151;
  background-color: #f3f4f6;
}

/* Body */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background: white;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-section {
  display: flex;
  gap: 24px;
}

.thumbnail-section {
  flex-shrink: 0;
}

.large-thumbnail {
  width: 256px;
  height: 256px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 256px;
  height: 256px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  margin-bottom: 16px;
}

.placeholder-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 18px;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.action-btn.primary {
  background-color: #10b981;
  color: white;
}

.action-btn.primary:hover {
  background-color: #059669;
}

.action-btn.secondary {
  background-color: #e5e7eb;
  color: #374151;
}

.action-btn.secondary:hover {
  background-color: #d1d5db;
}

.action-btn.danger {
  background-color: #ef4444;
  color: white;
}

.action-btn.danger:hover {
  background-color: #dc2626;
}

.basic-info {
  flex: 1;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 4px;
}

.info-value {
  color: #111827;
  font-weight: 500;
}

.status-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 14px;
  font-weight: 500;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-error {
  background-color: #fee2e2;
  color: #991b1b;
}

.badge-success {
  background-color: #d1fae5;
  color: #065f46;
}

/* Enhanced Content */
.enhanced-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
}

.classification-card {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 16px;
}

.classification-title {
  font-size: 18px;
  font-weight: 500;
  color: #111827;
  margin-bottom: 12px;
}

.classification-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.classification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.classification-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.classification-value {
  color: #111827;
  font-weight: 500;
}

.confidence-bar {
  position: relative;
  width: 128px;
  height: 16px;
  background-color: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background-color: #10b981;
  transition: all 0.3s;
}

.confidence-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.functionality-tags, .age-group-tags, .gender-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.functionality-tag, .age-group-tag, .gender-tag {
  padding: 4px 8px;
  background-color: #dbeafe;
  color: #1e40af;
  font-size: 12px;
  border-radius: 9999px;
}

/* Other sections */
.description-section, .resources-section, .issues-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.description-text {
  color: #374151;
  line-height: 1.6;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.resource-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.resource-type {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.resource-count {
  font-size: 18px;
  font-weight: bold;
  color: #111827;
}

.subsection-title {
  font-size: 18px;
  font-weight: 500;
  color: #111827;
}

.conflicts-list, .issues-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.conflict-item, .issue-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #f9fafb;
  border-radius: 8px;
}

/* Footer */
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
  background: white;
}

.footer-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.footer-btn.primary {
  background-color: #10b981;
  color: white;
}

.footer-btn.primary:hover {
  background-color: #059669;
}

.footer-btn.secondary {
  background-color: #e5e7eb;
  color: #374151;
}

.footer-btn.secondary:hover {
  background-color: #d1d5db;
}

/* Thumbnail Variations Section */
.variations-section {
  margin-top: 24px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.variations-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 16px;
}
</style>
