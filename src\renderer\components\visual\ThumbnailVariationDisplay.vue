<template>
  <div class="thumbnail-variation-display">
    <!-- Primary Thumbnail -->
    <div class="primary-thumbnail" @click="selectThumbnail(primaryThumbnail)">
      <img
        :src="selectedThumbnail.imageData"
        :alt="`${modName} - ${selectedThumbnail.resourceType}`"
        class="primary-image"
        @load="handleImageLoad"
        @error="handleImageError"
      />
      
      <!-- Variation Count Badge -->
      <div v-if="hasVariations" class="variation-count-badge">
        {{ totalVariations }}
      </div>
      
      <!-- Quality Indicator -->
      <div v-if="selectedThumbnail.isHighQuality" class="quality-badge">
        <SparklesIcon class="w-3 h-3" />
      </div>
    </div>
    
    <!-- Variation Grid (when expanded) -->
    <div v-if="showVariations && hasVariations" class="variation-grid" :class="gridSizeClass">
      <div
        v-for="(thumbnail, index) in displayedVariations"
        :key="thumbnail.id"
        class="variation-item"
        :class="{ 
          'selected': selectedThumbnail.id === thumbnail.id,
          'loading': !thumbnail.loaded 
        }"
        @click="selectThumbnail(thumbnail)"
        @mouseenter="handleVariationHover(thumbnail)"
        @mouseleave="handleVariationLeave"
      >
        <img
          :src="thumbnail.imageData"
          :alt="`${modName} - Variation ${index + 1}`"
          class="variation-image"
          loading="lazy"
          @load="() => markThumbnailLoaded(thumbnail)"
          @error="() => markThumbnailError(thumbnail)"
        />
        
        <!-- Variation Info -->
        <div class="variation-info">
          <span class="variation-type">{{ thumbnail.resourceType }}</span>
          <span class="variation-method">{{ thumbnail.extractionMethod }}</span>
        </div>
      </div>
      
      <!-- Show More Button -->
      <div v-if="hasMoreVariations" class="show-more-button" @click="showAllVariations">
        <PlusIcon class="w-4 h-4" />
        <span>+{{ remainingVariations }}</span>
      </div>
    </div>
    
    <!-- Variation Controls -->
    <div v-if="hasVariations" class="variation-controls">
      <button
        class="toggle-variations-btn"
        @click="toggleVariations"
        :aria-expanded="showVariations"
      >
        <ChevronDownIcon 
          class="w-4 h-4 transition-transform"
          :class="{ 'rotate-180': showVariations }"
        />
        <span>{{ showVariations ? 'Hide' : 'Show' }} Variations</span>
      </button>
      
      <!-- Display Mode Toggle -->
      <div class="display-mode-toggle">
        <button
          v-for="mode in displayModes"
          :key="mode.value"
          class="mode-btn"
          :class="{ 'active': displayMode === mode.value }"
          @click="setDisplayMode(mode.value)"
          :title="mode.label"
        >
          <component :is="mode.icon" class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import {
  SparklesIcon,
  ChevronDownIcon,
  PlusIcon,
  Squares2X2Icon,
  ViewColumnsIcon,
  QueueListIcon
} from '@heroicons/vue/24/outline';

import type { ThumbnailData } from '../../../services/visual/ThumbnailExtractionService';
import type { ThumbnailDisplayMode } from '../../../types/ModData';

// Props
interface Props {
  modName: string;
  primaryThumbnail: ThumbnailData;
  variations: ThumbnailData[];
  displayMode?: ThumbnailDisplayMode;
  maxVisibleVariations?: number;
  showVariationCount?: boolean;
  enableHoverPreview?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  displayMode: 'grid',
  maxVisibleVariations: 8,
  showVariationCount: true,
  enableHoverPreview: true
});

// Emits
const emit = defineEmits<{
  thumbnailSelected: [thumbnail: ThumbnailData];
  variationHover: [thumbnail: ThumbnailData];
  variationLeave: [];
}>();

// Reactive state
const selectedThumbnail = ref<ThumbnailData>(props.primaryThumbnail);
const showVariations = ref(false);
const showAllVariations = ref(false);
const displayMode = ref<ThumbnailDisplayMode>(props.displayMode);
const loadedThumbnails = ref(new Set<string>());

// Computed properties
const hasVariations = computed(() => props.variations.length > 0);

const totalVariations = computed(() => props.variations.length + 1); // +1 for primary

const displayedVariations = computed(() => {
  if (showAllVariations.value) {
    return props.variations;
  }
  return props.variations.slice(0, props.maxVisibleVariations);
});

const hasMoreVariations = computed(() => 
  !showAllVariations.value && props.variations.length > props.maxVisibleVariations
);

const remainingVariations = computed(() => 
  props.variations.length - props.maxVisibleVariations
);

const gridSizeClass = computed(() => {
  const count = displayedVariations.value.length;
  if (count <= 4) return 'grid-small';
  if (count <= 8) return 'grid-medium';
  return 'grid-large';
});

const displayModes = [
  { value: 'grid', label: 'Grid View', icon: Squares2X2Icon },
  { value: 'carousel', label: 'Carousel View', icon: ViewColumnsIcon },
  { value: 'expandable', label: 'Expandable View', icon: QueueListIcon }
];

// Methods
const selectThumbnail = (thumbnail: ThumbnailData) => {
  selectedThumbnail.value = thumbnail;
  emit('thumbnailSelected', thumbnail);
};

const toggleVariations = () => {
  showVariations.value = !showVariations.value;
};

const setDisplayMode = (mode: ThumbnailDisplayMode) => {
  displayMode.value = mode;
};

const handleVariationHover = (thumbnail: ThumbnailData) => {
  if (props.enableHoverPreview) {
    emit('variationHover', thumbnail);
  }
};

const handleVariationLeave = () => {
  if (props.enableHoverPreview) {
    emit('variationLeave');
  }
};

const markThumbnailLoaded = (thumbnail: ThumbnailData) => {
  loadedThumbnails.value.add(thumbnail.id);
};

const markThumbnailError = (thumbnail: ThumbnailData) => {
  console.warn(`Failed to load thumbnail variation: ${thumbnail.id}`);
};

const handleImageLoad = () => {
  // Handle primary image load
};

const handleImageError = () => {
  console.warn(`Failed to load primary thumbnail for ${props.modName}`);
};

// Watch for prop changes
watch(() => props.primaryThumbnail, (newThumbnail) => {
  selectedThumbnail.value = newThumbnail;
});
</script>

<style scoped>
.thumbnail-variation-display {
  @apply relative;
}

.primary-thumbnail {
  @apply relative cursor-pointer rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800;
  @apply hover:ring-2 hover:ring-blue-500 transition-all duration-200;
}

.primary-image {
  @apply w-full h-full object-cover;
}

.variation-count-badge {
  @apply absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full;
  @apply font-medium shadow-lg;
}

.quality-badge {
  @apply absolute top-2 left-2 bg-yellow-500 text-white p-1 rounded-full;
  @apply shadow-lg;
}

.variation-grid {
  @apply mt-3 grid gap-2;
}

.grid-small {
  @apply grid-cols-4;
}

.grid-medium {
  @apply grid-cols-4 sm:grid-cols-6;
}

.grid-large {
  @apply grid-cols-4 sm:grid-cols-6 md:grid-cols-8;
}

.variation-item {
  @apply relative cursor-pointer rounded-md overflow-hidden bg-gray-100 dark:bg-gray-800;
  @apply hover:ring-2 hover:ring-blue-400 transition-all duration-150;
  @apply aspect-square;
}

.variation-item.selected {
  @apply ring-2 ring-blue-600;
}

.variation-item.loading {
  @apply animate-pulse;
}

.variation-image {
  @apply w-full h-full object-cover;
}

.variation-info {
  @apply absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1;
  @apply opacity-0 hover:opacity-100 transition-opacity duration-200;
}

.variation-type {
  @apply block font-medium;
}

.variation-method {
  @apply block text-gray-300;
}

.show-more-button {
  @apply flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded-md;
  @apply cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors;
  @apply aspect-square text-gray-600 dark:text-gray-300;
}

.variation-controls {
  @apply mt-3 flex items-center justify-between;
}

.toggle-variations-btn {
  @apply flex items-center gap-2 px-3 py-1 text-sm text-gray-600 dark:text-gray-300;
  @apply hover:text-gray-900 dark:hover:text-white transition-colors;
}

.display-mode-toggle {
  @apply flex gap-1;
}

.mode-btn {
  @apply p-1 rounded text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200;
  @apply hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
}

.mode-btn.active {
  @apply text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900;
}
</style>
