<template>
  <div 
    class="skeleton-grid"
    :class="[
      `skeleton-grid--${size}`,
      { 'skeleton-grid--staggered': staggered }
    ]"
    role="presentation"
    aria-hidden="true"
    :aria-label="`Loading ${count} items`"
  >
    <ModCardSkeleton
      v-for="index in displayCount"
      :key="`skeleton-${index}`"
      :size="size"
      :animation="getAnimationForIndex(index)"
      :style="getSkeletonStyle(index)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import ModCardSkeleton from './ModCardSkeleton.vue';

interface Props {
  /** Number of skeleton items to show */
  count?: number;
  /** Size variant matching the mod card sizes */
  size?: 'small' | 'medium' | 'large';
  /** Animation type for skeleton elements */
  animation?: 'pulse' | 'wave' | 'shimmer' | 'none';
  /** Whether to stagger the animation timing */
  staggered?: boolean;
  /** Maximum number of skeletons to show (for performance) */
  maxCount?: number;
}

const props = withDefaults(defineProps<Props>(), {
  count: 12,
  size: 'medium',
  animation: 'shimmer',
  staggered: true,
  maxCount: 24,
});

const displayCount = computed(() => {
  return Math.min(props.count, props.maxCount);
});

const getAnimationForIndex = (index: number): string => {
  // For staggered animations, vary the animation type slightly
  if (props.staggered && props.animation !== 'none') {
    const animations = ['shimmer', 'pulse', 'wave'];
    return animations[(index - 1) % animations.length];
  }
  return props.animation;
};

const getSkeletonStyle = (index: number) => {
  if (!props.staggered) return {};
  
  // Stagger the animation delay for a more natural loading effect
  const delay = ((index - 1) * 100) % 1000; // Cycle every 10 items
  return {
    animationDelay: `${delay}ms`,
  };
};
</script>

<style scoped>
/* ===== SKELETON GRID BASE ===== */
.skeleton-grid {
  display: grid;
  gap: var(--space-6);
  width: 100%;
  align-items: start;
}

/* ===== SIZE-BASED GRID LAYOUTS ===== */
.skeleton-grid--small {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--space-4);
}

.skeleton-grid--medium {
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-6);
}

.skeleton-grid--large {
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: var(--space-8);
}

/* ===== STAGGERED ANIMATION SUPPORT ===== */
.skeleton-grid--staggered .mod-card-skeleton {
  opacity: 0;
  animation: skeleton-fade-in 0.6s ease-out forwards;
}

@keyframes skeleton-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .skeleton-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--space-6);
  }
}

@media (max-width: 768px) {
  .skeleton-grid--medium,
  .skeleton-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-4);
  }
}

@media (max-width: 640px) {
  .skeleton-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .skeleton-grid--small {
    gap: var(--space-3);
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .skeleton-grid--staggered .mod-card-skeleton {
    animation: none;
    opacity: 0.7;
  }
  
  @keyframes skeleton-fade-in {
    from, to {
      opacity: 0.7;
      transform: none;
    }
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.skeleton-grid {
  /* Use GPU acceleration for better performance */
  transform: translateZ(0);
  will-change: contents;
}

/* Limit the number of concurrent animations for performance */
.skeleton-grid--staggered .mod-card-skeleton:nth-child(n+13) {
  animation-delay: 0ms;
}
</style>
