<template>
  <div class="thumbnail-grid">
    <!-- <PERSON><PERSON> -->
    <div class="grid-header">
      <div class="grid-info">
        <span class="grid-count">{{ filteredThumbnails.length }}</span>
        <span class="grid-label">items</span>
        <span v-if="hasActiveFilters" class="grid-filtered">(filtered)</span>
      </div>
      
      <div class="grid-controls">
        <!-- View Size Controls -->
        <div class="size-controls">
          <button
            @click="thumbnailSize = 'small'"
            :class="{ active: thumbnailSize === 'small' }"
            class="size-btn"
            title="Small thumbnails"
          >
            <Squares2X2Icon class="w-4 h-4" />
          </button>
          <button
            @click="thumbnailSize = 'medium'"
            :class="{ active: thumbnailSize === 'medium' }"
            class="size-btn"
            title="Medium thumbnails"
          >
            <Square3Stack3DIcon class="w-4 h-4" />
          </button>
          <button
            @click="thumbnailSize = 'large'"
            :class="{ active: thumbnailSize === 'large' }"
            class="size-btn"
            title="Large thumbnails"
          >
            <RectangleStackIcon class="w-4 h-4" />
          </button>
        </div>
        
        <!-- Sort Controls -->
        <select v-model="sortBy" class="sort-select">
          <option value="name">Sort by Name</option>
          <option value="category">Sort by Category</option>
          <option value="size">Sort by File Size</option>
          <option value="date">Sort by Date</option>
        </select>
      </div>
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="grid-loading">
      <div class="loading-spinner"></div>
      <p>Extracting thumbnails...</p>
      <div class="loading-progress">
        <div class="progress-bar" :style="{ width: `${loadingProgress}%` }"></div>
      </div>
    </div>
    
    <!-- Empty State -->
    <div v-else-if="filteredThumbnails.length === 0" class="grid-empty">
      <PhotoIcon class="empty-icon" />
      <h3>No thumbnails available</h3>
      <p>No visual content found in the selected mods</p>
    </div>
    
    <!-- Thumbnail Grid -->
    <div v-else class="grid-container" :class="`size-${thumbnailSize}`">
      <div
        v-for="thumbnail in paginatedThumbnails"
        :key="thumbnail.id"
        class="thumbnail-item"
        @click="openPreview(thumbnail)"
        @keydown.enter="openPreview(thumbnail)"
        tabindex="0"
      >
        <!-- Thumbnail Image -->
        <div class="thumbnail-image">
          <img
            :src="thumbnail.imageData"
            :alt="thumbnail.modFileName"
            @load="onImageLoad(thumbnail)"
            @error="onImageError(thumbnail)"
            loading="lazy"
          />
          
          <!-- Fallback Indicator -->
          <div v-if="thumbnail.isFallback" class="fallback-badge">
            <CubeIcon class="w-3 h-3" />
          </div>
          
          <!-- Quality Indicator -->
          <div v-if="thumbnail.isHighQuality" class="quality-badge">
            <SparklesIcon class="w-3 h-3" />
          </div>
        </div>
        
        <!-- Thumbnail Info -->
        <div class="thumbnail-info">
          <h4 class="thumbnail-title" :title="thumbnail.modFileName">
            {{ getDisplayName(thumbnail) }}
          </h4>
          <p class="thumbnail-category">
            {{ formatCategory(thumbnail.category) }}
            <span v-if="thumbnail.subcategory">• {{ thumbnail.subcategory }}</span>
          </p>
          <div class="thumbnail-meta">
            <span class="meta-size">{{ formatFileSize(thumbnail.fileSize) }}</span>
            <span class="meta-format">{{ thumbnail.format.toUpperCase() }}</span>
            <span class="meta-dimensions">{{ thumbnail.width }}×{{ thumbnail.height }}</span>
          </div>
        </div>
        
        <!-- Hover Actions -->
        <div class="thumbnail-actions">
          <button
            @click.stop="openPreview(thumbnail)"
            class="action-btn preview-btn"
            title="Preview"
          >
            <EyeIcon class="w-4 h-4" />
          </button>
          <button
            @click.stop="showModDetails(thumbnail)"
            class="action-btn details-btn"
            title="Mod Details"
          >
            <InformationCircleIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
    
    <!-- Pagination -->
    <div v-if="totalPages > 1" class="grid-pagination">
      <button
        @click="currentPage--"
        :disabled="currentPage === 1"
        class="pagination-btn"
      >
        <ChevronLeftIcon class="w-4 h-4" />
        Previous
      </button>
      
      <span class="pagination-info">
        Page {{ currentPage }} of {{ totalPages }}
      </span>
      
      <button
        @click="currentPage++"
        :disabled="currentPage === totalPages"
        class="pagination-btn"
      >
        Next
        <ChevronRightIcon class="w-4 h-4" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import {
  Squares2X2Icon,
  Square3Stack3DIcon,
  RectangleStackIcon,
  PhotoIcon,
  EyeIcon,
  InformationCircleIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CubeIcon,
  SparklesIcon
} from '@heroicons/vue/24/outline';

import type { ThumbnailData } from '../../services/visual/ThumbnailExtractionService';

// Props
interface Props {
  thumbnails: ThumbnailData[];
  isLoading?: boolean;
  loadingProgress?: number;
  filters?: {
    category?: string;
    subcategory?: string;
    search?: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  loadingProgress: 0,
  filters: () => ({})
});

// Emits
const emit = defineEmits<{
  previewThumbnail: [thumbnail: ThumbnailData];
  showModDetails: [thumbnail: ThumbnailData];
  thumbnailLoad: [thumbnail: ThumbnailData];
  thumbnailError: [thumbnail: ThumbnailData];
}>();

// Reactive state
const thumbnailSize = ref<'small' | 'medium' | 'large'>('medium');
const sortBy = ref<'name' | 'category' | 'size' | 'date'>('name');
const currentPage = ref(1);
const itemsPerPage = computed(() => {
  switch (thumbnailSize.value) {
    case 'small': return 50;
    case 'medium': return 24;
    case 'large': return 12;
    default: return 24;
  }
});

// Computed properties
const hasActiveFilters = computed(() => {
  return !!(props.filters.category || props.filters.subcategory || props.filters.search);
});

const filteredThumbnails = computed(() => {
  let filtered = [...props.thumbnails];
  
  // Apply category filter
  if (props.filters.category) {
    filtered = filtered.filter(t => t.category === props.filters.category);
  }
  
  // Apply subcategory filter
  if (props.filters.subcategory) {
    filtered = filtered.filter(t => t.subcategory === props.filters.subcategory);
  }
  
  // Apply search filter
  if (props.filters.search) {
    const search = props.filters.search.toLowerCase();
    filtered = filtered.filter(t => 
      t.modFileName.toLowerCase().includes(search) ||
      t.category.toLowerCase().includes(search) ||
      (t.subcategory && t.subcategory.toLowerCase().includes(search))
    );
  }
  
  // Apply sorting
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'name':
        return a.modFileName.localeCompare(b.modFileName);
      case 'category':
        return a.category.localeCompare(b.category);
      case 'size':
        return b.fileSize - a.fileSize;
      case 'date':
        return a.modFileName.localeCompare(b.modFileName); // Fallback to name
      default:
        return 0;
    }
  });
  
  return filtered;
});

const totalPages = computed(() => {
  return Math.ceil(filteredThumbnails.value.length / itemsPerPage.value);
});

const paginatedThumbnails = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredThumbnails.value.slice(start, end);
});

// Methods
const openPreview = (thumbnail: ThumbnailData) => {
  emit('previewThumbnail', thumbnail);
};

const showModDetails = (thumbnail: ThumbnailData) => {
  emit('showModDetails', thumbnail);
};

const onImageLoad = (thumbnail: ThumbnailData) => {
  emit('thumbnailLoad', thumbnail);
};

const onImageError = (thumbnail: ThumbnailData) => {
  emit('thumbnailError', thumbnail);
};

const getDisplayName = (thumbnail: ThumbnailData): string => {
  // Remove file extension and clean up the name
  return thumbnail.modFileName
    .replace(/\.(package|ts4script)$/i, '')
    .replace(/[_-]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};

const formatCategory = (category: string): string => {
  return category
    .replace(/[_-]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Watch for filter changes to reset pagination
watch(() => props.filters, () => {
  currentPage.value = 1;
}, { deep: true });

// Watch for thumbnail size changes to reset pagination
watch(thumbnailSize, () => {
  currentPage.value = 1;
});
</script>

<style scoped>
.thumbnail-grid {
  @apply w-full;
}

.grid-header {
  @apply flex items-center justify-between mb-6 p-4 bg-surface rounded-lg border;
}

.grid-info {
  @apply flex items-center gap-2 text-sm;
}

.grid-count {
  @apply font-semibold text-primary;
}

.grid-label {
  @apply text-muted;
}

.grid-filtered {
  @apply text-xs text-warning bg-warning/10 px-2 py-1 rounded;
}

.grid-controls {
  @apply flex items-center gap-4;
}

.size-controls {
  @apply flex items-center gap-1 p-1 bg-background rounded border;
}

.size-btn {
  @apply p-2 rounded transition-colors;
}

.size-btn:hover {
  @apply bg-surface;
}

.size-btn.active {
  @apply bg-primary text-primary-foreground;
}

.sort-select {
  @apply px-3 py-2 border rounded bg-background text-sm;
}

.grid-loading {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mb-4;
}

.loading-progress {
  @apply w-64 h-2 bg-surface rounded-full overflow-hidden mt-4;
}

.progress-bar {
  @apply h-full bg-primary transition-all duration-300;
}

.grid-empty {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.empty-icon {
  @apply w-16 h-16 text-muted mb-4;
}

.grid-container {
  @apply grid gap-4;
}

.grid-container.size-small {
  @apply grid-cols-8 gap-2;
}

.grid-container.size-medium {
  @apply grid-cols-6 gap-4;
}

.grid-container.size-large {
  @apply grid-cols-4 gap-6;
}

.thumbnail-item {
  @apply relative bg-surface rounded-lg border overflow-hidden cursor-pointer transition-all duration-200 group;
}

.thumbnail-item:hover {
  @apply border-primary shadow-lg transform scale-105;
}

.thumbnail-item:focus {
  @apply outline-none ring-2 ring-primary ring-offset-2;
}

.thumbnail-image {
  @apply relative aspect-square overflow-hidden;
}

.thumbnail-image img {
  @apply w-full h-full object-cover;
}

.fallback-badge {
  @apply absolute top-2 left-2 bg-warning text-warning-foreground p-1 rounded;
}

.quality-badge {
  @apply absolute top-2 right-2 bg-success text-success-foreground p-1 rounded;
}

.thumbnail-info {
  @apply p-3;
}

.thumbnail-title {
  @apply font-medium text-sm mb-1 truncate;
}

.thumbnail-category {
  @apply text-xs text-muted mb-2;
}

.thumbnail-meta {
  @apply flex items-center gap-2 text-xs text-muted;
}

.meta-size,
.meta-format,
.meta-dimensions {
  @apply px-1 py-0.5 bg-background rounded;
}

.thumbnail-actions {
  @apply absolute inset-0 bg-black/50 flex items-center justify-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity;
}

.action-btn {
  @apply p-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors;
}

.grid-pagination {
  @apply flex items-center justify-center gap-4 mt-8 py-4;
}

.pagination-btn {
  @apply flex items-center gap-2 px-4 py-2 border rounded hover:bg-surface transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.pagination-info {
  @apply text-sm text-muted;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .grid-container.size-small {
    @apply grid-cols-6;
  }
  
  .grid-container.size-medium {
    @apply grid-cols-4;
  }
  
  .grid-container.size-large {
    @apply grid-cols-3;
  }
}

@media (max-width: 768px) {
  .grid-container.size-small {
    @apply grid-cols-4;
  }
  
  .grid-container.size-medium {
    @apply grid-cols-3;
  }
  
  .grid-container.size-large {
    @apply grid-cols-2;
  }
  
  .grid-header {
    @apply flex-col gap-4;
  }
}

@media (max-width: 480px) {
  .grid-container.size-small {
    @apply grid-cols-3;
  }
  
  .grid-container.size-medium {
    @apply grid-cols-2;
  }
  
  .grid-container.size-large {
    @apply grid-cols-1;
  }
}
</style>
