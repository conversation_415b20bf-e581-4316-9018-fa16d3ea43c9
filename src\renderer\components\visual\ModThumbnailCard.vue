<template>
  <div :class="['mod-thumbnail-card', viewMode, { selected: isSelected }]" @click="handleClick">
    <!-- Thumbnail Section -->
    <div class="thumbnail-container">
      <div v-if="thumbnailLoading" class="thumbnail-loading">
        <div class="loading-spinner"></div>
      </div>

      <!-- Primary Thumbnail Display (main card shows only primary) -->
      <img
        v-if="thumbnailUrl"
        :src="thumbnailUrl"
        :alt="`${mod.fileName} thumbnail`"
        class="thumbnail-image"
        @load="handleThumbnailLoad"
        @error="handleThumbnailError"
      />

      <!-- Variation indicator badge (shows if multiple variations exist) -->
      <div v-if="hasMultipleVariations" class="variation-indicator" title="Multiple color variations available">
        <span class="variation-count">+{{ thumbnailVariations.length }}</span>
      </div>

      <div v-else class="thumbnail-placeholder">
        <component :is="getPlaceholderIcon()" class="placeholder-icon" />
        <span class="placeholder-text">{{ getPlaceholderText() }}</span>
      </div>
      
      <!-- Overlay badges -->
      <div class="thumbnail-overlay">
        <div class="badge-container">
          <span v-if="mod.hasConflicts" class="badge badge-warning" title="Has conflicts">
            <ExclamationTriangleIcon class="w-3 h-3" />
          </span>
          <span v-if="mod.isCorrupted" class="badge badge-error" title="Corrupted file">
            <XCircleIcon class="w-3 h-3" />
          </span>
          <span v-if="isHighQuality" class="badge badge-success" title="High quality">
            <SparklesIcon class="w-3 h-3" />
          </span>
        </div>
        
        <div class="action-buttons">
          <button
            v-if="viewMode === 'grid'"
            @click.stop="toggleFavorite"
            :class="['action-btn', { active: isFavorite }]"
            title="Add to favorites"
          >
            <HeartIcon class="w-4 h-4" />
          </button>
          <button
            @click.stop="showQuickActions = !showQuickActions"
            class="action-btn"
            title="Quick actions"
          >
            <EllipsisVerticalIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <!-- Quick Actions Menu -->
      <div v-if="showQuickActions" class="quick-actions-menu" @click.stop>
        <button @click="openInExplorer" class="quick-action">
          <FolderOpenIcon class="w-4 h-4" />
          Open in Explorer
        </button>
        <button @click="copyPath" class="quick-action">
          <ClipboardIcon class="w-4 h-4" />
          Copy Path
        </button>
        <button @click="showDetails" class="quick-action">
          <InformationCircleIcon class="w-4 h-4" />
          View Details
        </button>
        <button v-if="mod.isCorrupted" @click="deleteMod" class="quick-action danger">
          <TrashIcon class="w-4 h-4" />
          Delete
        </button>
      </div>
    </div>
    
    <!-- Content Section -->
    <div class="content-section">
      <div class="mod-header">
        <h3 class="mod-title" :title="mod.fileName">{{ displayName }}</h3>
        <div class="mod-meta">
          <span class="category-badge" :class="getCategoryClass()">
            {{ mod.category || 'Unknown' }}
          </span>
          <span class="file-size">{{ formatFileSize(mod.fileSize) }}</span>
        </div>
      </div>
      
      <div v-if="viewMode === 'list'" class="mod-details">
        <p class="mod-description">{{ mod.description || 'No description available' }}</p>
        <div class="mod-stats">
          <div class="stat-item">
            <span class="stat-label">Resources:</span>
            <span class="stat-value">{{ mod.resourceCount || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Last Modified:</span>
            <span class="stat-value">{{ formatDate(mod.lastModified) }}</span>
          </div>
          <div v-if="mod.author" class="stat-item">
            <span class="stat-label">Author:</span>
            <span class="stat-value">{{ mod.author }}</span>
          </div>
        </div>
      </div>
      
      <!-- Enhanced Content Analysis Display -->
      <div v-if="hasEnhancedContent" class="enhanced-content">
        <div class="content-tags">
          <span
            v-for="tag in getContentTags()"
            :key="tag"
            class="content-tag"
          >
            {{ tag }}
          </span>
        </div>
        
        <div v-if="mod.objectClassification" class="object-info">
          <span class="object-type">{{ mod.objectClassification.specificType }}</span>
          <span v-if="mod.objectClassification.roomAssignment" class="room-assignment">
            for {{ mod.objectClassification.roomAssignment }}
          </span>
        </div>
      </div>
      
      <!-- Progress bar for analysis -->
      <div v-if="isAnalyzing" class="analysis-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${analysisProgress}%` }"></div>
        </div>
        <span class="progress-text">Analyzing... {{ analysisProgress }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import {
  ExclamationTriangleIcon,
  XCircleIcon,
  SparklesIcon,
  HeartIcon,
  EllipsisVerticalIcon,
  FolderOpenIcon,
  ClipboardIcon,
  InformationCircleIcon,
  TrashIcon,
  DocumentIcon,
  UserIcon,
  HomeIcon,
  CogIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';

import type { ModData } from '../../../types/ModData';
import type { ThumbnailData } from '../../../services/visual/ThumbnailExtractionService';
import ThumbnailVariationDisplay from './ThumbnailVariationDisplay.vue';

// Props
interface Props {
  mod: ModData;
  viewMode: 'grid' | 'list';
  isSelected?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false
});

// Emits
const emit = defineEmits<{
  click: [mod: ModData];
  thumbnailError: [modId: string];
  favorite: [modId: string, isFavorite: boolean];
  delete: [modId: string];
}>();

// Reactive state
const thumbnailLoading = ref(false);

// Computed thumbnail URL from enhanced data
const thumbnailUrl = computed(() => {
  // Use enhanced thumbnail data if available
  if (primaryThumbnail.value?.imageData) {
    return primaryThumbnail.value.imageData;
  }
  // Fallback to legacy thumbnail data
  if (props.mod.thumbnailData) {
    return props.mod.thumbnailData;
  }
  if (props.mod.thumbnailUrl) {
    return props.mod.thumbnailUrl;
  }
  return null;
});
const showQuickActions = ref(false);
const isFavorite = ref(false);
const isAnalyzing = ref(false);
const analysisProgress = ref(0);

// Computed properties
const displayName = computed(() => {
  const name = props.mod.fileName;
  // Remove file extension and clean up the name
  return name.replace(/\.(package|ts4script)$/i, '').replace(/_/g, ' ');
});

const isHighQuality = computed(() => {
  // Determine if this is a high-quality mod based on various factors
  const hasHighResources = (props.mod.resourceCount || 0) > 50;
  const hasLargeSize = (props.mod.fileSize || 0) > 1024 * 1024; // > 1MB
  const hasEnhancedAnalysis = props.mod.objectClassification?.confidence > 0.8;
  
  return hasHighResources || hasLargeSize || hasEnhancedAnalysis;
});

const hasEnhancedContent = computed(() => {
  return props.mod.objectClassification || props.mod.universalClassification;
});

// Enhanced thumbnail support
const hasThumbnails = computed(() => {
  const result = props.mod.thumbnails && props.mod.thumbnails.length > 0;
  console.log(`🔍 [ModThumbnailCard] ${props.mod.fileName} - hasThumbnails: ${result}, thumbnails: ${props.mod.thumbnails?.length || 0}`);
  return result;
});

const primaryThumbnail = computed(() => {
  const result = props.mod.primaryThumbnail || (props.mod.thumbnails && props.mod.thumbnails[0]);
  console.log(`🎯 [ModThumbnailCard] ${props.mod.fileName} - primaryThumbnail: ${result ? 'Yes' : 'No'}`);
  return result;
});

const thumbnailVariations = computed(() => {
  const result = props.mod.thumbnailVariations || [];
  console.log(`🎨 [ModThumbnailCard] ${props.mod.fileName} - variations: ${result.length}`);
  return result;
});

const hasMultipleVariations = computed(() => {
  const result = props.mod.hasMultipleVariations || thumbnailVariations.value.length > 0;
  console.log(`🔢 [ModThumbnailCard] ${props.mod.fileName} - hasMultipleVariations: ${result}`);
  return result;
});

// Methods
const handleClick = (event: Event) => {
  console.log('🖱️ [ModThumbnailCard] handleClick called for:', props.mod.fileName);
  console.log('🖱️ [ModThumbnailCard] Event target:', event.target);
  console.log('🖱️ [ModThumbnailCard] Event currentTarget:', event.currentTarget);
  console.log('🖱️ [ModThumbnailCard] Mod data being emitted:', {
    fileName: props.mod.fileName,
    id: props.mod.id,
    hasData: !!props.mod
  });

  emit('click', props.mod);
  console.log('📤 [ModThumbnailCard] click event emitted successfully');
};

const handleThumbnailLoad = () => {
  thumbnailLoading.value = false;
};

const handleThumbnailError = () => {
  thumbnailLoading.value = false;
  // thumbnailUrl is now computed, can't set it directly
  emit('thumbnailError', props.mod.id);
};

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value;
  emit('favorite', props.mod.id, isFavorite.value);
};

const openInExplorer = () => {
  // Emit event to parent to handle file explorer opening
  window.electronAPI?.openInExplorer?.(props.mod.filePath);
  showQuickActions.value = false;
};

const copyPath = async () => {
  try {
    await navigator.clipboard.writeText(props.mod.filePath);
    // Could show a toast notification here
  } catch (error) {
    console.error('Failed to copy path:', error);
  }
  showQuickActions.value = false;
};

const showDetails = () => {
  emit('click', props.mod);
  showQuickActions.value = false;
};

// Enhanced thumbnail event handlers
const handleThumbnailSelected = (thumbnail: ThumbnailData) => {
  console.log(`Selected thumbnail variation: ${thumbnail.resourceType}`);
  // TODO: Implement thumbnail selection logic
  // For now, just log the selection
};

const handleVariationHover = (thumbnail: ThumbnailData) => {
  // Optional: Preview thumbnail on hover
  console.log(`Hovering over variation: ${thumbnail.resourceType}`);
};

const handleVariationLeave = () => {
  // Optional: Reset preview on hover leave
};

const deleteMod = () => {
  if (confirm(`Are you sure you want to delete "${props.mod.fileName}"?`)) {
    emit('delete', props.mod.id);
  }
  showQuickActions.value = false;
};

const getPlaceholderIcon = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return UserIcon;
  if (category.includes('build') || category.includes('buy')) return HomeIcon;
  if (category.includes('script')) return CogIcon;
  if (category.includes('gameplay')) return PuzzlePieceIcon;
  
  return DocumentIcon;
};

const getPlaceholderText = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas')) return 'CAS';
  if (category.includes('build') || category.includes('buy')) return 'Build/Buy';
  if (category.includes('script')) return 'Script';
  if (category.includes('gameplay')) return 'Gameplay';
  
  return 'Mod';
};

const getCategoryClass = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return 'category-cas';
  if (category.includes('build') || category.includes('buy')) return 'category-build';
  if (category.includes('script')) return 'category-script';
  if (category.includes('gameplay')) return 'category-gameplay';
  
  return 'category-other';
};

const getContentTags = (): string[] => {
  const tags: string[] = [];
  
  if (props.mod.objectClassification) {
    if (props.mod.objectClassification.category) {
      tags.push(props.mod.objectClassification.category);
    }
    if (props.mod.objectClassification.functionality) {
      tags.push(...props.mod.objectClassification.functionality);
    }
  }
  
  if (props.mod.universalClassification) {
    if (props.mod.universalClassification.subcategory) {
      tags.push(props.mod.universalClassification.subcategory);
    }
    if (props.mod.universalClassification.ageGroups) {
      tags.push(...props.mod.universalClassification.ageGroups);
    }
  }
  
  return tags.slice(0, 3); // Limit to 3 tags for display
};

const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

const formatDate = (timestamp: number | undefined): string => {
  if (!timestamp) return 'Unknown';
  
  const date = new Date(timestamp);
  return date.toLocaleDateString();
};

const loadThumbnail = () => {
  // Thumbnail loading is now handled by ModDashboard via IPC
  // This component just displays the data provided by the parent
  console.log(`🖼️ [ModThumbnailCard] Loading thumbnail for ${props.mod.fileName}:`, {
    hasThumbnailUrl: !!thumbnailUrl.value,
    hasThumbnails: hasThumbnails.value,
    hasVariations: hasMultipleVariations.value
  });
};

// Lifecycle
onMounted(() => {
  console.log(`🚀 [ModThumbnailCard] Mounted for ${props.mod.fileName}:`, {
    hasThumbnails: props.mod.thumbnails?.length || 0,
    hasVariations: props.mod.thumbnailVariations?.length || 0,
    hasMultipleVariations: props.mod.hasMultipleVariations,
    primaryThumbnail: !!props.mod.primaryThumbnail,
    thumbnailData: !!props.mod.thumbnailData,
    filePath: !!props.mod.filePath
  });
  loadThumbnail();
});

// Close quick actions when clicking outside
const handleClickOutside = (event: Event) => {
  if (!event.target || !(event.target as Element).closest('.quick-actions-menu')) {
    showQuickActions.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
/* Modern Card Design - Apple-inspired with Sims 4 theming */
.mod-thumbnail-card {
  @apply cursor-pointer overflow-hidden transition-all duration-500 ease-out relative;

  /* Modern card foundation */
  background: linear-gradient(145deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.04);

  /* Professional multi-layer shadow system */
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    0 1px 4px rgba(0, 0, 0, 0.02),
    0 0 1px rgba(0, 0, 0, 0.04);

  /* Performance optimizations */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform, box-shadow, border-color;
}

.mod-thumbnail-card:hover {
  /* Sophisticated hover animation */
  transform: translateY(-8px) scale(1.03) translateZ(0);
  border-color: rgba(45, 159, 43, 0.2);

  /* Enhanced depth with multiple shadow layers */
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.08),
    0 8px 16px rgba(0, 0, 0, 0.04),
    0 4px 8px rgba(0, 0, 0, 0.02),
    0 0 0 1px rgba(45, 159, 43, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.mod-thumbnail-card:active {
  /* Subtle press feedback */
  transform: translateY(-6px) scale(1.01) translateZ(0);
  transition-duration: 150ms;
}

.mod-thumbnail-card.selected {
  /* Premium selection state with Sims 4 plumbob green */
  border: 3px solid #2D9F2B;
  transform: translateY(-4px) scale(1.02) translateZ(0);

  /* Sophisticated selection glow */
  box-shadow:
    0 24px 48px rgba(45, 159, 43, 0.15),
    0 12px 24px rgba(45, 159, 43, 0.1),
    0 6px 12px rgba(45, 159, 43, 0.08),
    0 0 0 6px rgba(45, 159, 43, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);

  /* Subtle inner glow */
  background: linear-gradient(145deg,
    rgba(45, 159, 43, 0.02) 0%,
    rgba(255, 255, 255, 1) 50%,
    rgba(45, 159, 43, 0.01) 100%);
}

.mod-thumbnail-card.selected:hover {
  /* Enhanced selection hover */
  transform: translateY(-6px) scale(1.03) translateZ(0);
}

.mod-thumbnail-card.list {
  @apply flex items-center p-4 space-x-4;
}

.mod-thumbnail-card.grid {
  @apply flex flex-col;
}

/* Modern Thumbnail Container with sophisticated design */
.thumbnail-container {
  @apply relative overflow-hidden;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.8) 0%,
    rgba(241, 245, 249, 0.9) 50%,
    rgba(226, 232, 240, 0.8) 100%);
  position: relative;
}

.thumbnail-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(0, 0, 0, 0.02) 100%);
  pointer-events: none;
  z-index: 1;
}

.mod-thumbnail-card.grid .thumbnail-container {
  @apply aspect-square;
  border-radius: 16px 16px 0 0;
  box-shadow:
    inset 0 1px 3px rgba(0, 0, 0, 0.06),
    inset 0 -1px 0 rgba(255, 255, 255, 0.4);
}

.mod-thumbnail-card.list .thumbnail-container {
  @apply w-16 h-16 flex-shrink-0;
  border-radius: 14px;
  box-shadow:
    inset 0 1px 3px rgba(0, 0, 0, 0.06),
    inset 0 -1px 0 rgba(255, 255, 255, 0.4);
}

.thumbnail-loading {
  @apply w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700;
}

.loading-spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin;
}

.thumbnail-image {
  @apply w-full h-full object-cover transition-all duration-500 ease-out;
  position: relative;
  z-index: 2;
}

.mod-thumbnail-card:hover .thumbnail-image {
  transform: scale(1.08);
  filter: brightness(1.05) contrast(1.02) saturate(1.1);
}

.thumbnail-placeholder {
  @apply w-full h-full flex flex-col items-center justify-center
         text-gray-500 dark:text-gray-400 transition-all duration-500;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.95) 0%,
    rgba(241, 245, 249, 1) 50%,
    rgba(226, 232, 240, 0.95) 100%);
  backdrop-filter: blur(20px);
  position: relative;
  z-index: 2;
}

.thumbnail-placeholder::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle,
    rgba(45, 159, 43, 0.03) 0%,
    transparent 70%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.mod-thumbnail-card:hover .thumbnail-placeholder::before {
  opacity: 1;
}

.mod-thumbnail-card:hover .thumbnail-placeholder {
  background: linear-gradient(135deg,
    rgba(45, 159, 43, 0.08) 0%,
    rgba(45, 159, 43, 0.12) 50%,
    rgba(45, 159, 43, 0.08) 100%);
  color: rgba(45, 159, 43, 0.8);
}

.placeholder-icon {
  @apply w-8 h-8 mb-2;
}

.mod-thumbnail-card.list .placeholder-icon {
  @apply w-6 h-6 mb-1;
}

.placeholder-text {
  @apply text-sm font-medium;
}

.mod-thumbnail-card.list .placeholder-text {
  @apply text-xs;
}

/* Sophisticated overlay with premium effects */
.thumbnail-overlay {
  @apply absolute inset-0 opacity-0 transition-all duration-500 ease-out
         flex flex-col justify-between p-3;
  background: linear-gradient(180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 40%,
    rgba(0, 0, 0, 0.4) 100%);
  pointer-events: none; /* Allow clicks to pass through to main card */
  backdrop-filter: blur(4px);
  z-index: 3;
}

.mod-thumbnail-card:hover .thumbnail-overlay {
  @apply opacity-100;
  background: linear-gradient(180deg,
    rgba(45, 159, 43, 0.05) 0%,
    rgba(0, 0, 0, 0.15) 40%,
    rgba(0, 0, 0, 0.5) 100%);
}

/* Re-enable pointer events for interactive elements within overlay */
.thumbnail-overlay .badge-container,
.thumbnail-overlay .action-buttons,
.thumbnail-overlay .action-btn {
  pointer-events: auto;
}

.badge-container {
  @apply flex space-x-1;
}

.badge {
  @apply flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.badge-warning {
  @apply bg-yellow-500 text-white;
}

.badge-error {
  @apply bg-red-500 text-white;
}

.badge-success {
  @apply bg-green-500 text-white;
}

/* Enhanced action buttons with Apple-inspired micro-interactions */
.action-buttons {
  @apply flex space-x-2 self-end opacity-0 transition-all duration-300 ease-out;
  transform: translateY(4px);
}

.mod-thumbnail-card:hover .action-buttons {
  @apply opacity-100;
  transform: translateY(0);
}

.action-btn {
  @apply p-2 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300
         rounded-full shadow-sm hover:shadow-md transition-all duration-200 ease-out;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
}

.action-btn:hover {
  @apply bg-white dark:bg-gray-700;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.action-btn.active {
  @apply text-red-500;
  background: rgba(239, 68, 68, 0.1);
}

/* Quick Actions Menu */
.quick-actions-menu {
  @apply absolute top-full right-0 mt-1 bg-white dark:bg-gray-800 
         border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg
         py-1 z-10 min-w-40;
}

.quick-action {
  @apply w-full flex items-center space-x-2 px-3 py-2 text-sm
         text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700
         transition-colors;
}

.quick-action.danger {
  @apply text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900;
}

/* Premium Content Section with sophisticated design */
.content-section {
  @apply p-6; /* Generous padding for luxury feel */
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(250, 251, 252, 1) 100%);
  position: relative;
  border-radius: 0 0 16px 16px;
}

.content-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.04) 50%,
    transparent 100%);
}

.mod-thumbnail-card.list .content-section {
  @apply flex-1 p-0 pl-5;
  background: transparent;
  border-radius: 0;
}

.mod-thumbnail-card.list .content-section::before {
  display: none;
}

.mod-header {
  @apply mb-4; /* Optimal spacing for visual hierarchy */
}

.mod-title {
  /* Premium typography with sophisticated details */
  @apply text-lg font-semibold mb-2;
  color: #1a1a1a;
  line-height: 1.3;
  letter-spacing: -0.025em;
  font-weight: 600;

  /* Advanced text overflow handling */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;

  /* Smooth transitions with multiple properties */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mod-thumbnail-card:hover .mod-title {
  color: #2D9F2B;
  transform: translateY(-1px);
  text-shadow: 0 2px 4px rgba(45, 159, 43, 0.1);
}

.mod-thumbnail-card.list .mod-title {
  @apply text-base;
  -webkit-line-clamp: 1;
}

.mod-meta {
  @apply flex items-center space-x-2;
}

/* Premium badges with sophisticated glass morphism */
.category-badge {
  @apply px-3 py-1.5 rounded-full text-xs font-semibold;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px);

  /* Advanced transitions with spring physics */
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow:
    0 3px 6px rgba(0, 0, 0, 0.06),
    0 1px 3px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.category-badge:hover {
  transform: translateY(-2px) scale(1.08);
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.12),
    0 4px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.category-cas {
  /* Sims 4 CAS purple with professional gradient */
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  @apply text-white;
  border-color: rgba(139, 92, 246, 0.3);
}

.category-build {
  /* Sims 4 Build/Buy blue with professional gradient */
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  @apply text-white;
  border-color: rgba(59, 130, 246, 0.3);
}

.category-script {
  /* Sims 4 plumbob green with professional gradient */
  background: linear-gradient(135deg, #2D9F2B 0%, #22C55E 100%);
  @apply text-white;
  border-color: rgba(45, 159, 43, 0.3);
}

.category-gameplay {
  /* Sims 4 gameplay orange with professional gradient */
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  @apply text-white;
  border-color: rgba(245, 158, 11, 0.3);
}

.category-other {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200;
}

.file-size {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

/* List View Details */
.mod-details {
  @apply mt-2;
}

.mod-description {
  @apply text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2;
}

.mod-stats {
  @apply flex space-x-4 text-xs text-gray-500 dark:text-gray-400;
}

.stat-item {
  @apply flex space-x-1;
}

.stat-label {
  @apply font-medium;
}

/* Enhanced Content */
.enhanced-content {
  @apply mt-2 space-y-2;
}

.content-tags {
  @apply flex flex-wrap gap-1;
}

.content-tag {
  @apply px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
         text-xs rounded-full;
}

.object-info {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.object-type {
  @apply font-medium;
}

.room-assignment {
  @apply text-gray-500 dark:text-gray-500;
}

/* Analysis Progress */
.analysis-progress {
  @apply mt-2;
}

.progress-bar {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-1;
}

.progress-fill {
  @apply bg-green-500 h-2 rounded-full transition-all duration-300;
}

.progress-text {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

/* Premium variation indicator with glass morphism */
.variation-indicator {
  @apply absolute top-3 right-3 text-xs font-semibold
         px-3 py-1.5 rounded-full transition-all duration-500 ease-out;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    0 2px 6px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  color: rgba(45, 159, 43, 0.9);
  z-index: 4;
}

.mod-thumbnail-card:hover .variation-indicator {
  background: rgba(45, 159, 43, 0.95);
  color: white;
  transform: scale(1.08) translateY(-2px);
  box-shadow:
    0 8px 20px rgba(45, 159, 43, 0.3),
    0 4px 10px rgba(45, 159, 43, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.variation-count {
  @apply text-white;
  font-weight: 700;
}

/* Professional Focus States for Accessibility */
.mod-thumbnail-card:focus-visible {
  outline: none;
  border-color: #2D9F2B;
  box-shadow:
    0 0 0 3px rgba(45, 159, 43, 0.2),
    0 8px 25px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px) scale(1.01);
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .mod-thumbnail-card {
    background: #1a1a1a;
    border-color: rgba(255, 255, 255, 0.08);
  }

  .mod-title {
    color: #ffffff;
  }

  .mod-thumbnail-card:hover .mod-title {
    color: #22C55E;
  }

  .content-section {
    background: linear-gradient(180deg,
      rgba(26, 26, 26, 0.95) 0%,
      rgba(26, 26, 26, 1) 100%);
  }

  .thumbnail-placeholder {
    background: linear-gradient(135deg,
      rgba(38, 38, 38, 0.8) 0%,
      rgba(45, 45, 45, 0.9) 50%,
      rgba(38, 38, 38, 0.8) 100%);
  }
}
</style>
